#!/usr/bin/env python3
"""
Script kiểm tra và sửa metadata cho TTS tiếng Việt
Đảm bảo format đúng và giữ lại dấu câu cảm xúc
"""

import os
import re
from pathlib import Path

def check_metadata_format(metadata_path):
    """Kiểm tra format metadata"""
    print(f"🔍 Checking metadata: {metadata_path}")
    
    if not os.path.exists(metadata_path):
        print(f"❌ File not found: {metadata_path}")
        return False
    
    issues = []
    valid_lines = 0
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 Total lines: {len(lines)}")
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
            
        # Kiểm tra format: ID|text|normalized_text
        parts = line.split('|')
        if len(parts) != 3:
            issues.append(f"Line {i}: Wrong format (expected 3 parts, got {len(parts)})")
            continue
        
        audio_id, text, normalized_text = parts
        
        # Kiểm tra audio ID
        if not audio_id.strip():
            issues.append(f"Line {i}: Empty audio ID")
            continue
        
        # Kiểm tra text
        if not text.strip():
            issues.append(f"Line {i}: Empty text")
            continue
        
        # Kiểm tra file audio tương ứng
        audio_file = Path(metadata_path).parent / "wavs" / f"{audio_id.strip()}.wav"
        if not audio_file.exists():
            issues.append(f"Line {i}: Audio file not found: {audio_file}")
        
        valid_lines += 1
    
    print(f"✅ Valid lines: {valid_lines}")
    
    if issues:
        print(f"⚠️  Found {len(issues)} issues:")
        for issue in issues[:10]:  # Hiển thị 10 lỗi đầu
            print(f"  - {issue}")
        if len(issues) > 10:
            print(f"  ... and {len(issues) - 10} more issues")
        return False
    else:
        print("✅ Metadata format is correct!")
        return True

def fix_metadata_issues(metadata_path, output_path=None):
    """Sửa các vấn đề trong metadata"""
    if output_path is None:
        output_path = metadata_path + ".fixed"
    
    print(f"🔧 Fixing metadata issues...")
    
    fixed_lines = []
    skipped_lines = []
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
        
        parts = line.split('|')
        if len(parts) != 3:
            skipped_lines.append(f"Line {i}: Wrong format")
            continue
        
        audio_id, text, normalized_text = parts
        audio_id = audio_id.strip()
        text = text.strip()
        normalized_text = normalized_text.strip()
        
        # Kiểm tra file audio
        audio_file = Path(metadata_path).parent / "wavs" / f"{audio_id}.wav"
        if not audio_file.exists():
            skipped_lines.append(f"Line {i}: Audio file not found")
            continue
        
        # Sửa text nếu cần
        # Loại bỏ dấu chấm thừa ở cuối normalized_text
        if normalized_text.endswith('..'):
            normalized_text = normalized_text[:-1]
        
        # Đảm bảo text và normalized_text giống nhau (cho single speaker)
        if text != normalized_text:
            normalized_text = text
        
        # Tạo dòng mới
        fixed_line = f"{audio_id}|{text}|{normalized_text}"
        fixed_lines.append(fixed_line)
    
    # Ghi file mới
    with open(output_path, 'w', encoding='utf-8') as f:
        for line in fixed_lines:
            f.write(line + '\n')
    
    print(f"✅ Fixed metadata saved to: {output_path}")
    print(f"📊 Fixed lines: {len(fixed_lines)}")
    print(f"⚠️  Skipped lines: {len(skipped_lines)}")
    
    if skipped_lines:
        print("Skipped lines:")
        for skip in skipped_lines[:5]:
            print(f"  - {skip}")
    
    return output_path

def analyze_text_content(metadata_path):
    """Phân tích nội dung text để đảm bảo tương thích với cleaner"""
    print(f"📝 Analyzing text content...")
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    all_chars = set()
    punctuation_count = {}
    
    for line in lines:
        parts = line.strip().split('|')
        if len(parts) >= 2:
            text = parts[1]
            all_chars.update(text)
            
            # Đếm dấu câu
            for char in text:
                if char in ",.!?'\":-":
                    punctuation_count[char] = punctuation_count.get(char, 0) + 1
    
    # Ký tự tiếng Việt hợp lệ
    valid_chars = set("aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz0123456789 ,.!?'\":-AÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬBCDĐEÉÈẺẼẸÊẾỀỂỄỆFGHIÍÌỈĨỊJKLMNOÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢPQRSTUÚÙỦŨỤƯỨỪỬỮỰVWXYÝỲỶỸỴZ")
    
    invalid_chars = all_chars - valid_chars
    
    print(f"📊 Character analysis:")
    print(f"  Total unique characters: {len(all_chars)}")
    print(f"  Valid characters: {len(all_chars & valid_chars)}")
    print(f"  Invalid characters: {len(invalid_chars)}")
    
    if invalid_chars:
        print(f"  Invalid chars found: {sorted(invalid_chars)}")
    
    print(f"📝 Punctuation usage:")
    for punct, count in sorted(punctuation_count.items()):
        print(f"  '{punct}': {count} times")
    
    return len(invalid_chars) == 0

def main():
    """Hàm chính"""
    print("🇻🇳 Vietnamese TTS Metadata Checker & Fixer")
    print("=" * 50)
    
    # Đường dẫn metadata
    metadata_path = "MyTTSDataset/metadata.txt"
    
    if not os.path.exists(metadata_path):
        print(f"❌ Metadata file not found: {metadata_path}")
        print("Please ensure your dataset is in the correct location.")
        return
    
    # 1. Kiểm tra format
    print("\n1️⃣ Checking metadata format...")
    format_ok = check_metadata_format(metadata_path)
    
    # 2. Phân tích nội dung text
    print("\n2️⃣ Analyzing text content...")
    text_ok = analyze_text_content(metadata_path)
    
    # 3. Sửa lỗi nếu cần
    if not format_ok:
        print("\n3️⃣ Fixing metadata issues...")
        fixed_path = fix_metadata_issues(metadata_path)
        print(f"✅ Fixed metadata saved to: {fixed_path}")
        print("Please replace the original metadata.txt with the fixed version.")
    else:
        print("\n✅ Metadata is ready for training!")
    
    # 4. Tóm tắt
    print("\n📋 Summary:")
    print(f"  Format check: {'✅ PASS' if format_ok else '❌ FAIL'}")
    print(f"  Text analysis: {'✅ PASS' if text_ok else '⚠️  WARNING'}")
    
    if format_ok and text_ok:
        print("\n🎉 Your dataset is ready for TTS training!")
        print("You can now run the training script.")
    else:
        print("\n🔧 Please fix the issues above before training.")

if __name__ == "__main__":
    main()
