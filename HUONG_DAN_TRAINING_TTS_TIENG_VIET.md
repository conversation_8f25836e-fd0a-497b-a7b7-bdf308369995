# 🇻🇳 Hướng Dẫn Training TTS Tiếng Việt trên Google Colab

## 📋 Tổng Quan

Hướng dẫn này giúp bạn huấn luyện mô hình Text-to-Speech (TTS) tiếng Việt sử dụng VITS trên Google Colab, với khả năng **giữ lại dấu câu để bảo toàn cảm xúc** trong giọng nói.

## ✅ Những Gì Đã Được Cải Thiện

### 🔧 **Sửa Lỗi Cấu Hình:**
1. **Formatter**: Đã sửa từ `"ljsotch"` → `"ljspeech"`
2. **Model**: Chuyển từ Tacotron2 → VITS (tối ưu hơn cho Colab)
3. **Language code**: Đổi từ `"vi-vn"` → `"vi"`
4. **Batch size**: Giảm xuống phù hợp với GPU Colab

### 🎯 **Tối Ưu Cho Tiếng Việt:**
1. **Text Cleaner**: <PERSON><PERSON><PERSON> thiện để giữ lại dấu câu cảm xúc
2. **Character Set**: Bao gồm đầy đủ ký tự tiếng Việt + số + dấu câu
3. **Punctuation**: Mở rộng `",.!?'\":-"` để giữ cảm xúc
4. **Audio Config**: Tối ưu cho tiếng Việt

### 🚀 **Tối Ưu Cho Colab:**
1. **Mixed Precision**: Bật để tiết kiệm memory
2. **Batch Size**: Giảm để tránh OOM
3. **Workers**: Tối ưu cho Colab environment
4. **Gradient Clipping**: Ổn định training

## 📁 Cấu Trúc Dataset

Đảm bảo dataset của bạn có cấu trúc như sau:

```
/content/drive/MyDrive/TTS/MyTTSDataset/
├── metadata.txt
└── wavs/
    ├── 1.wav
    ├── 2.wav
    ├── 3.wav
    └── ...
```

### 📝 Format Metadata

File `metadata.txt` phải có format:
```
1|Text tiếng Việt với dấu câu!|Text tiếng Việt với dấu câu!
2|Câu thứ hai, có dấu phẩy.|Câu thứ hai, có dấu phẩy.
3|Câu hỏi thế này được không?|Câu hỏi thế này được không?
```

**Lưu ý**: Giữ nguyên dấu câu `,.!?'\":-` để bảo toàn cảm xúc!

## 🚀 Cách Sử Dụng

### Option 1: Jupyter Notebook (Khuyến nghị)

1. **Upload notebook lên Colab:**
   - Mở Google Colab
   - Upload file `Vietnamese_TTS_Training_Colab.ipynb`
   - Chọn GPU runtime (Runtime → Change runtime type → GPU)

2. **Chạy từng cell theo thứ tự:**
   - Cell 1: Setup environment
   - Cell 2: Mount Google Drive
   - Cell 3: Configuration
   - Cell 4: Training

### Option 2: Python Script

1. **Upload script lên Colab:**
   ```python
   # Upload file train_vietnamese_tts_colab.py
   !python train_vietnamese_tts_colab.py
   ```

### Option 3: Sử dụng File Hiện Tại

1. **Upload file `train.py` đã được sửa:**
   ```python
   !python train.py
   ```

## ⚙️ Cấu Hình Chi Tiết

### 🎵 Audio Configuration
```python
VitsAudioConfig(
    sample_rate=22050,    # Tần số mẫu chuẩn
    win_length=1024,      # Độ dài cửa sổ
    hop_length=256,       # Bước nhảy
    num_mels=80,          # Số mel bands
    mel_fmin=0,           # Tần số mel min
    mel_fmax=None,        # Tần số mel max
)
```

### 🤖 Model Configuration
```python
VitsArgs(
    use_speaker_embedding=False,  # Single speaker
    use_sdp=True,                 # Stochastic Duration Predictor
    noise_scale=0.667,            # Noise cho inference
    noise_scale_dp=1.0,           # Noise cho duration predictor
    length_scale=1.0,             # Scale độ dài
)
```

### 📦 Training Configuration
```python
VitsConfig(
    batch_size=8,                 # Tối ưu cho Colab
    eval_batch_size=4,
    epochs=1000,                  # Số epochs
    mixed_precision=True,         # Tiết kiệm memory
    text_cleaner="vietnamese_cleaners",
    use_phonemes=False,           # Không dùng phonemes
)
```

## 🧹 Text Cleaner Tối Ưu

```python
def vietnamese_cleaners(text):
    """Giữ lại dấu câu để bảo toàn cảm xúc"""
    text = text.lower()
    
    # Giữ lại: chữ cái tiếng Việt, số, dấu câu cảm xúc
    text = re.sub(r'[^a-zA-Zàáảãạăắằẳẵặâấầẩẫậèéèẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ0-9\s,.!?\'\":-]', '', text)
    
    # Chuẩn hóa khoảng trắng và dấu câu
    text = re.sub(r'\s+', ' ', text).strip()
    text = re.sub(r'([,.!?])\s*', r'\1 ', text)
    
    return text
```

## 📊 Monitoring Training

### 🎯 Các Chỉ Số Quan Trọng:
- **Generator Loss**: Nên giảm dần từ ~10 → ~2
- **Discriminator Loss**: Nên ổn định quanh 1-2
- **Mel Loss**: Nên giảm dần từ ~5 → ~1
- **Duration Loss**: Nên giảm dần

### 📈 Timeline Dự Kiến:
- **0-50 epochs**: Loss giảm nhanh, audio còn nhiễu
- **50-200 epochs**: Audio bắt đầu rõ ràng
- **200-500 epochs**: Chất lượng tốt, cần fine-tune
- **500+ epochs**: Chất lượng cao, có thể dừng

### 🔍 Kiểm Tra Chất Lượng:
```python
# Model sẽ tự động tạo test samples với các câu:
test_sentences = [
    "Xin chào, tôi là trợ lý ảo tiếng Việt!",
    "Hôm nay trời đẹp quá, bạn có muốn đi dạo không?",
    "Cảm ơn bạn rất nhiều vì đã giúp đỡ tôi.",
    "Chúc bạn một ngày tốt lành và hạnh phúc!",
    "Tôi yêu Việt Nam và tiếng Việt của chúng ta."
]
```

## 💡 Tips & Tricks

### 🚀 Tối Ưu Performance:
1. **Sử dụng GPU T4 hoặc V100** trên Colab Pro
2. **Giảm batch_size** nếu gặp OOM error
3. **Bật mixed_precision** để tiết kiệm memory
4. **Backup checkpoints** thường xuyên

### 🎵 Cải Thiện Chất Lượng:
1. **Dataset chất lượng cao**: 22kHz, ít noise
2. **Giữ nguyên dấu câu** trong metadata
3. **Cân bằng độ dài câu** (5-15 giây)
4. **Đủ dữ liệu**: Tối thiểu 1 giờ, tốt nhất 3-5 giờ

### 🔧 Troubleshooting:
1. **OOM Error**: Giảm batch_size xuống 4 hoặc 2
2. **Slow Training**: Kiểm tra GPU có được sử dụng không
3. **Bad Audio**: Kiểm tra sample rate và audio quality
4. **NaN Loss**: Giảm learning rate hoặc tăng gradient clipping

## 📁 Output Files

Sau khi training, bạn sẽ có:
```
/content/drive/MyDrive/TTS/MyOutput/
├── config.json              # Cấu hình model
├── best_model.pth           # Model tốt nhất
├── checkpoint_*.pth         # Checkpoints
├── events.out.tfevents.*    # TensorBoard logs
└── test_audios/             # Audio samples test
```

## 🎯 Sử Dụng Model Sau Training

```python
from TTS.api import TTS

# Load model đã train
tts = TTS(model_path="/path/to/best_model.pth", 
          config_path="/path/to/config.json")

# Tạo audio
tts.tts_to_file(text="Xin chào! Tôi là giọng nói tiếng Việt.", 
                file_path="output.wav")
```

## 🆘 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra cấu trúc dataset
2. Xem logs để tìm lỗi
3. Thử giảm batch_size
4. Kiểm tra GPU memory usage

**Chúc bạn training thành công! 🎉**
