{"output_path": "/content/drive/MyDrive/TTS/MyOutput", "logger_uri": null, "run_name": "run", "project_name": null, "run_description": "🐸Coqui trainer run.", "print_step": 25, "plot_step": 100, "model_param_stats": false, "wandb_entity": null, "dashboard_logger": "tensorboard", "save_on_interrupt": true, "log_model_step": null, "save_step": 100, "save_n_checkpoints": 5, "save_checkpoints": true, "save_all_best": false, "save_best_after": 0, "target_loss": null, "print_eval": true, "test_delay_epochs": -1, "run_eval": true, "run_eval_steps": null, "distributed_backend": "nccl", "distributed_url": "tcp://localhost:54321", "mixed_precision": false, "precision": "fp16", "epochs": 1000, "batch_size": 64, "eval_batch_size": 16, "grad_clip": 5.0, "scheduler_after_epoch": true, "lr": 0.0001, "optimizer": "RAdam", "optimizer_params": {"betas": [0.9, 0.998], "weight_decay": 1e-06}, "lr_scheduler": "NoamLR", "lr_scheduler_params": {"warmup_steps": 4000}, "use_grad_scaler": false, "allow_tf32": false, "cudnn_enable": true, "cudnn_deterministic": false, "cudnn_benchmark": false, "training_seed": 54321, "model": "tacotron2", "num_loader_workers": 2, "num_eval_loader_workers": 2, "use_noise_augment": false, "audio": {"fft_size": 1024, "win_length": 1024, "hop_length": 256, "frame_shift_ms": null, "frame_length_ms": null, "stft_pad_mode": "reflect", "sample_rate": 22050, "resample": false, "preemphasis": 0.0, "ref_level_db": 20, "do_sound_norm": false, "log_func": "np.log", "do_trim_silence": true, "trim_db": 60.0, "do_rms_norm": false, "db_level": null, "power": 1.5, "griffin_lim_iters": 60, "num_mels": 80, "mel_fmin": 0.0, "mel_fmax": 8000, "spec_gain": 1.0, "do_amp_to_db_linear": true, "do_amp_to_db_mel": true, "pitch_fmax": 640.0, "pitch_fmin": 1.0, "signal_norm": false, "min_level_db": -100, "symmetric_norm": true, "max_norm": 4.0, "clip_norm": true, "stats_path": null}, "use_phonemes": false, "phonemizer": null, "phoneme_language": "vi-vn", "compute_input_seq_cache": false, "text_cleaner": "basic_cleaners", "enable_eos_bos_chars": false, "test_sentences_file": "", "phoneme_cache_path": "/content/drive/MyDrive/TTS/MyOutput/phoneme_cache", "characters": {"characters_class": "TTS.tts.utils.text.characters.Graphemes", "vocab_dict": null, "pad": "<PAD>", "eos": "<EOS>", "bos": "<BOS>", "blank": "<BLNK>", "characters": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz", "punctuations": "!'(),-.:;? ", "phonemes": null, "is_unique": false, "is_sorted": true}, "add_blank": false, "batch_group_size": 0, "loss_masking": true, "min_audio_len": 1, "max_audio_len": Infinity, "min_text_len": 1, "max_text_len": Infinity, "compute_f0": false, "compute_energy": false, "compute_linear_spec": false, "precompute_num_workers": 0, "start_by_longest": false, "shuffle": false, "drop_last": false, "datasets": [{"formatter": "l<PERSON><PERSON><PERSON><PERSON>", "dataset_name": "", "path": "/content/drive/MyDrive/TTS/MyTTSDataset", "meta_file_train": "metadata.txt", "ignored_speakers": null, "language": "vi-vn", "phonemizer": "", "meta_file_val": "", "meta_file_attn_mask": ""}], "test_sentences": ["It took me quite a long time to develop a voice, and now that I have it I'm not going to be silent.", "Be a voice, not an echo.", "I'm sorry <PERSON>. I'm afraid I can't do that.", "This cake is great. It's so delicious and moist.", "Prior to November 22, 1963."], "eval_split_max_size": null, "eval_split_size": 0.01, "use_speaker_weighted_sampler": false, "speaker_weighted_sampler_alpha": 1.0, "use_language_weighted_sampler": false, "language_weighted_sampler_alpha": 1.0, "use_length_weighted_sampler": false, "length_weighted_sampler_alpha": 1.0, "use_gst": false, "gst": null, "gst_style_input": null, "use_capacitron_vae": false, "capacitron_vae": null, "num_speakers": 1, "num_chars": 67, "r": 2, "gradual_training": null, "memory_size": -1, "prenet_type": "original", "prenet_dropout": true, "prenet_dropout_at_inference": false, "stopnet": true, "separate_stopnet": true, "stopnet_pos_weight": 0.2, "max_decoder_steps": 10000, "encoder_in_features": 512, "decoder_in_features": 512, "decoder_output_dim": 80, "out_channels": 80, "attention_type": "dynamic_convolution", "attention_heads": null, "attention_norm": "sigmoid", "attention_win": false, "windowing": false, "use_forward_attn": false, "forward_attn_mask": false, "transition_agent": false, "location_attn": true, "bidirectional_decoder": false, "double_decoder_consistency": false, "ddc_r": 6, "speakers_file": null, "use_speaker_embedding": false, "speaker_embedding_dim": 512, "use_d_vector_file": false, "d_vector_file": false, "d_vector_dim": null, "seq_len_norm": false, "decoder_loss_alpha": 0.25, "postnet_loss_alpha": 0.25, "postnet_diff_spec_alpha": 0, "decoder_diff_spec_alpha": 0, "decoder_ssim_alpha": 0, "postnet_ssim_alpha": 0, "ga_alpha": 0.0, "github_branch": "inside_docker"}