from TTS.tts.utils.text.phonemizers.base import BasePhonemizer
from TTS.tts.utils.text.phonemizers.espeak_wrapper import ESpeak
from TTS.tts.utils.text.phonemizers.gruut_wrapper import Gruut
# from TTS.tts.utils.text.phonemizers.ja_jp_phonemizer import JA_JP_Phonemizer
from TTS.tts.utils.text.phonemizers.ko_kr_phonemizer import KO_KR_Phonemizer
from TTS.tts.utils.text.phonemizers.zh_cn_phonemizer import ZH_CN_Phonemizer
from TTS.tts.utils.text.vietnamese.phonemizer import VI_Phonemizer

# Define the default language to phonemizer mapping
DEF_LANG_TO_PHONEMIZER = {
    "en-us": "gruut",
    "en-gb": "gruut",
    "fr-fr": "gruut",
    "es-es": "gruut",
    "de-de": "gruut",
    "zh-cn": "zh-cn",
    "ko-kr": "ko-kr",
    "vi": "vi",
    # Add other languages as needed
}

def get_phonemizer_by_name(name: str, **kwargs) -> BasePhonemizer:
    """Get phonemizer by name.

    Args:
        name (str): One of the supported phonemizers.
            espeak, espeak-ng, gruut, ja-jp, zh-cn, ko-kr, vi

        kwargs: Keyword arguments to pass to the phonemizer.

    Returns:
        BasePhonemizer: Phonemizer instance.

    Raises:
        ValueError: If phonemizer is not supported.
    """
    if name == "espeak" or name == "espeak-ng":
        return ESpeak(**kwargs)
    elif name == "gruut":
        return Gruut(**kwargs)
    elif name == "ja-jp":
        # Import JA_JP_Phonemizer only when needed to avoid early import errors
        try:
            from TTS.tts.utils.text.phonemizers.ja_jp_phonemizer import JA_JP_Phonemizer
            return JA_JP_Phonemizer(**kwargs)
        except ImportError:
            raise ImportError("Japanese phonemizer requires mecab-python3 and unidic-lite. Install with: pip install mecab-python3 unidic-lite")
    elif name == "zh-cn":
        return ZH_CN_Phonemizer(**kwargs)
    elif name == "ko-kr":
        return KO_KR_Phonemizer(**kwargs)
    elif name == "vi":
        return VI_Phonemizer(**kwargs)
    else:
        raise ValueError(f"Phonemizer {name} not supported.")

if __name__ == "__main__":
    print(DEF_LANG_TO_PHONEMIZER)
