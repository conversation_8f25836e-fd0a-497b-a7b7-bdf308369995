{"cells": [{"cell_type": "code", "execution_count": null, "id": "4d50310e-f094-42e0-af30-1e42b13ceb95", "metadata": {}, "outputs": [], "source": ["#@title # Setup\n", "# Imports used through the rest of the notebook.\n", "import torch\n", "import torchaudio\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "import IPython\n", "\n", "from TTS.tts.models.tortoise import TextToSpeech\n", "from TTS.tts.layers.tortoise.audio_utils import load_audio, load_voice, load_voices\n", "\n", "# This will download all the models used by Tortoise from the HuggingFace hub.\n", "tts = TextToSpeech()"]}, {"cell_type": "code", "execution_count": null, "id": "e126c3c3-d90a-492f-b5bb-0d86587f15cc", "metadata": {}, "outputs": [], "source": ["# This is the text that will be spoken.\n", "text = \"Joining two modalities results in a surprising increase in generalization! What would happen if we combined them all?\" #@param {type:\"string\"}\n", "#@markdown Show code for multiline text input\n", "# Here's something for the poetically inclined.. (set text=)\n", "\"\"\"\n", "Then took the other, as just as fair,\n", "And having perhaps the better claim,\n", "Because it was grassy and wanted wear;\n", "Though as for that the passing there\n", "Had worn them really about the same,\"\"\"\n", "\n", "# Pick a \"preset mode\" to determine quality. Options: {\"ultra_fast\", \"fast\" (default), \"standard\", \"high_quality\"}. See docs in api.py\n", "preset = \"fast\" #@param [\"ultra_fast\", \"fast\", \"standard\", \"high_quality\"]"]}, {"cell_type": "code", "execution_count": null, "id": "9413f553-5bd0-4820-bad4-edd7fd7d2370", "metadata": {}, "outputs": [], "source": ["%ls ../TTS/tts/utils/assets/tortoise/voices/\n", "import IPython\n", "IPython.display.Audio(filename='../TTS/tts/utils/assets/tortoise/voices/lj/1.wav')"]}, {"cell_type": "code", "execution_count": null, "id": "96a98ae5-313b-40d1-9311-5a785f2c9a4e", "metadata": {}, "outputs": [], "source": ["#@markdown Pick one of the voices from the output above\n", "voice = 'lj' #@param {type:\"string\"}\n", "\n", "#@markdown Load it and send it through Tortoise.\n", "voice_samples, conditioning_latents = load_voice(voice)\n", "gen = tts.tts_with_preset(text, voice_samples=voice_samples, conditioning_latents=conditioning_latents, \n", "                          preset=preset)\n", "torchaudio.save('generated.wav', gen.squeeze(0).cpu(), 24000)\n", "IPython.display.Audio('generated.wav')"]}, {"cell_type": "code", "execution_count": null, "id": "04e473e5-c489-4a78-aa11-03e89a778ed8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}