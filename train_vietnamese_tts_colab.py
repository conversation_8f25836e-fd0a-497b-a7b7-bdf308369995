#!/usr/bin/env python3
"""
Script huấn luyện TTS tiếng Việt tối ưu cho Google Colab
Sử dụng VITS model với cấu hình tối ưu cho GPU Colab

Hướng dẫn sử dụng:
1. Upload dataset lên Google Drive
2. Mount Google Drive trong Colab
3. Chạy script này

Author: TTS Vietnamese Training
"""

import os
import sys
import re
import torch
from pathlib import Path

# Kiểm tra GPU
print("🔍 Checking GPU availability...")
if torch.cuda.is_available():
    print(f"✅ GPU available: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
else:
    print("⚠️  No GPU available. Training will be very slow!")

# Cài đặt dependencies nếu cần
def install_requirements():
    """Cài đặt các package cần thiết cho Colab"""
    try:
        import trainer
        from TTS.tts.configs.vits_config import VitsConfig
        print("✅ TTS packages already installed")
    except ImportError:
        print("📦 Installing TTS packages...")
        os.system("pip install TTS trainer")
        print("✅ Installation completed")

# Mount Google Drive
def mount_drive():
    """Mount Google Drive trong Colab"""
    try:
        from google.colab import drive
        drive.mount('/content/drive')
        print("✅ Google Drive mounted successfully")
        return True
    except ImportError:
        print("ℹ️  Not running in Colab, skipping drive mount")
        return False

# Setup paths
def setup_paths():
    """Thiết lập đường dẫn cho Colab"""
    # Đường dẫn mặc định cho Colab
    base_path = "/content/drive/MyDrive/TTS"
    
    paths = {
        'dataset': f"{base_path}/MyTTSDataset",
        'output': f"{base_path}/MyOutput",
        'metadata': f"{base_path}/MyTTSDataset/metadata.txt",
        'wavs': f"{base_path}/MyTTSDataset/wavs"
    }
    
    # Tạo thư mục output nếu chưa có
    os.makedirs(paths['output'], exist_ok=True)
    
    # Kiểm tra dataset
    if not os.path.exists(paths['metadata']):
        print(f"❌ Metadata file not found: {paths['metadata']}")
        print("📋 Please ensure your dataset structure is:")
        print("   /content/drive/MyDrive/TTS/MyTTSDataset/")
        print("   ├── metadata.txt")
        print("   └── wavs/")
        print("       ├── 1.wav")
        print("       ├── 2.wav")
        print("       └── ...")
        return None
    
    if not os.path.exists(paths['wavs']):
        print(f"❌ Wavs directory not found: {paths['wavs']}")
        return None
    
    # Đếm số file audio
    wav_files = list(Path(paths['wavs']).glob("*.wav"))
    print(f"📊 Found {len(wav_files)} audio files")
    
    return paths

def vietnamese_cleaners(text):
    """
    Text cleaner tối ưu cho tiếng Việt
    Giữ lại dấu câu để bảo toàn cảm xúc
    """
    # Chuyển về chữ thường
    text = text.lower()
    
    # Giữ lại ký tự tiếng Việt, số, và dấu câu quan trọng
    text = re.sub(r'[^a-zA-Zàáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ0-9\s,.!?\'\":-]', '', text)
    
    # Chuẩn hóa khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()
    
    # Chuẩn hóa dấu câu
    text = re.sub(r'([,.!?])\s*', r'\1 ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def create_config(paths):
    """Tạo cấu hình VITS tối ưu cho tiếng Việt và Colab"""
    
    from trainer import Trainer, TrainerArgs
    from TTS.tts.configs.shared_configs import BaseDatasetConfig
    from TTS.tts.configs.vits_config import VitsConfig
    from TTS.tts.models.vits import VitsArgs, VitsAudioConfig
    
    # Dataset config
    dataset_config = BaseDatasetConfig(
        formatter="ljspeech",
        meta_file_train="metadata.txt",
        path=paths['dataset'],
        language="vi"
    )
    
    # Audio config tối ưu cho tiếng Việt
    audio_config = VitsAudioConfig(
        sample_rate=22050,
        win_length=1024,
        hop_length=256,
        num_mels=80,
        mel_fmin=0,
        mel_fmax=None,
    )
    
    # VITS model args
    vits_args = VitsArgs(
        use_speaker_embedding=False,
        use_sdp=True,
        noise_scale=0.667,
        noise_scale_dp=1.0,
        length_scale=1.0,
    )
    
    # Main config
    config = VitsConfig(
        model_args=vits_args,
        audio=audio_config,
        run_name="vits_vietnamese_colab",
        
        # Batch size tối ưu cho Colab
        batch_size=12,  # Giảm để tránh OOM
        eval_batch_size=6,
        batch_group_size=5,
        
        # Workers cho Colab
        num_loader_workers=2,
        num_eval_loader_workers=2,
        
        # Training settings
        run_eval=True,
        test_delay_epochs=10,
        epochs=1000,
        
        # Text processing
        text_cleaner="vietnamese_cleaners",
        use_phonemes=False,
        phoneme_language="vi",
        phoneme_cache_path=os.path.join(paths['output'], "phoneme_cache"),
        compute_input_seq_cache=True,
        
        # Logging
        print_step=25,
        print_eval=True,
        save_step=500,
        save_checkpoints=True,
        
        # Optimization cho Colab
        mixed_precision=True,
        grad_clip=[1000, 1000],
        
        # Paths
        output_path=paths['output'],
        datasets=[dataset_config],
        
        # Test sentences
        test_sentences=[
            "Xin chào, tôi là trợ lý ảo tiếng Việt!",
            "Hôm nay trời đẹp quá, bạn có muốn đi dạo không?",
            "Cảm ơn bạn rất nhiều vì đã giúp đỡ tôi.",
            "Chúc bạn một ngày tốt lành và hạnh phúc!",
            "Tôi yêu Việt Nam và tiếng Việt của chúng ta."
        ],
        
        # Character set cho tiếng Việt
        characters={
            "characters": "aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz0123456789",
            "punctuations": ",.!?'\":-",
            "pad": "<PAD>",
            "eos": "<EOS>",
            "bos": "<BOS>",
            "blank": "<BLNK>",
        }
    )
    
    return config, dataset_config

def main():
    """Hàm chính để chạy training"""
    print("🚀 Starting Vietnamese TTS Training on Colab")
    print("=" * 50)
    
    # 1. Cài đặt packages
    install_requirements()
    
    # 2. Mount Google Drive
    mount_drive()
    
    # 3. Setup paths
    paths = setup_paths()
    if paths is None:
        print("❌ Failed to setup paths. Please check your dataset structure.")
        return
    
    # 4. Import sau khi cài đặt
    from trainer import Trainer, TrainerArgs
    from TTS.tts.datasets import load_tts_samples
    from TTS.tts.models.vits import Vits
    from TTS.tts.utils.text.tokenizer import TTSTokenizer
    from TTS.utils.audio import AudioProcessor
    from TTS.tts.utils.text import cleaners
    
    # 5. Tạo config
    print("⚙️  Creating configuration...")
    config, dataset_config = create_config(paths)
    
    # 6. Đăng ký cleaner
    cleaners.vietnamese_cleaners = vietnamese_cleaners
    
    # 7. Khởi tạo components
    print("🔧 Initializing components...")
    ap = AudioProcessor.init_from_config(config)
    tokenizer, config = TTSTokenizer.init_from_config(config)
    
    # 8. Load dataset
    print("📊 Loading dataset...")
    train_samples, eval_samples = load_tts_samples(
        dataset_config,
        eval_split=True,
        eval_split_max_size=config.eval_split_max_size,
        eval_split_size=config.eval_split_size,
    )
    
    print(f"✅ Dataset loaded: {len(train_samples)} train, {len(eval_samples)} eval samples")
    
    # 9. Khởi tạo model
    print("🤖 Initializing VITS model...")
    model = Vits(config, ap, tokenizer)
    
    # 10. Setup trainer
    trainer_args = TrainerArgs(
        restore_path=None,
        skip_train_epoch=False,
        start_with_eval=False,
        grad_accum_steps=1,
    )
    
    # 11. Hiển thị thông tin training
    print("\n🎯 Training Configuration:")
    print(f"📁 Output: {paths['output']}")
    print(f"🌍 Language: Vietnamese")
    print(f"🎵 Sample rate: {config.audio.sample_rate}Hz")
    print(f"📦 Batch size: {config.batch_size}")
    print(f"🔄 Epochs: {config.epochs}")
    print(f"💾 Mixed precision: {config.mixed_precision}")
    print(f"🔧 Model: VITS")
    
    # 12. Bắt đầu training
    print("\n🚀 Starting training...")
    trainer = Trainer(
        trainer_args,
        config,
        paths['output'],
        model=model,
        train_samples=train_samples,
        eval_samples=eval_samples
    )
    
    trainer.fit()

if __name__ == "__main__":
    main()
