# Model API
Model API provides you a set of functions that easily make your model compatible with the `Trainer`,
`Synthesizer` and `ModelZoo`.

## Base TTS Model

```{eval-rst}
.. autoclass:: TTS.model.BaseTrainerModel
    :members:
```

## Base tts Model

```{eval-rst}
.. autoclass:: TTS.tts.models.base_tts.BaseTTS
    :members:
```

## Base vocoder Model

```{eval-rst}
.. autoclass:: TTS.vocoder.models.base_vocoder.BaseVocoder
    :members:
```