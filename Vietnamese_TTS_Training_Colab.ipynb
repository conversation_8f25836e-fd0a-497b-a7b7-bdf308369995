{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🇻🇳 Vietnamese TTS Training with VITS on Google Colab\n", "\n", "Notebook này gi<PERSON>p bạn huấn luyện mô hình TTS tiếng Việt sử dụng VITS trên Google Colab.\n", "\n", "## ✨ <PERSON><PERSON><PERSON> n<PERSON>ng:\n", "- ✅ T<PERSON>i ưu cho GPU Colab (T4/V100)\n", "- ✅ G<PERSON>ữ lại dấu câu để bảo toàn cảm xúc\n", "- ✅ Text cleaner tùy chỉnh cho tiếng <PERSON>\n", "- ✅ <PERSON><PERSON><PERSON> hình VITS tối ưu\n", "- ✅ Monitoring và logging chi tiết\n", "\n", "## 📋 <PERSON><PERSON><PERSON> cầu:\n", "1. Dataset tiếng Việt với format LJSpeech\n", "2. Google Drive để lưu trữ\n", "3. GPU runtime tr<PERSON><PERSON>\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🔧 1. Setup Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> tra GPU\n", "import torch\n", "print(\"🔍 Checking GPU...\")\n", "if torch.cuda.is_available():\n", "    print(f\"✅ GPU: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"💾 Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "else:\n", "    print(\"⚠️ No GPU available!\")\n", "\n", "# Cài đặt TTS\n", "print(\"\\n📦 Installing TTS...\")\n", "!pip install -q TTS trainer\n", "print(\"✅ Installation completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "mount_drive"}, "source": ["## 📁 2. Mount Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive_code"}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Ki<PERSON>m tra cấu trúc dataset\n", "import os\n", "from pathlib import Path\n", "\n", "dataset_path = \"/content/drive/MyDrive/TTS/MyTTSDataset\"\n", "metadata_file = f\"{dataset_path}/metadata.txt\"\n", "wavs_dir = f\"{dataset_path}/wavs\"\n", "\n", "print(\"🔍 Checking dataset structure...\")\n", "if os.path.exists(metadata_file):\n", "    print(f\"✅ Metadata file found: {metadata_file}\")\n", "    \n", "    # Đếm số dòng trong metadata\n", "    with open(metadata_file, 'r', encoding='utf-8') as f:\n", "        lines = f.readlines()\n", "    print(f\"📊 Metadata entries: {len(lines)}\")\n", "    \n", "    # <PERSON><PERSON><PERSON> thị vài dòng đầu\n", "    print(\"\\n📝 Sample metadata entries:\")\n", "    for i, line in enumerate(lines[:3]):\n", "        print(f\"  {i+1}: {line.strip()}\")\n", "else:\n", "    print(f\"❌ Metadata file not found: {metadata_file}\")\n", "\n", "if os.path.exists(wavs_dir):\n", "    wav_files = list(Path(wavs_dir).glob(\"*.wav\"))\n", "    print(f\"✅ Audio files found: {len(wav_files)}\")\n", "else:\n", "    print(f\"❌ Wavs directory not found: {wavs_dir}\")\n", "\n", "# Tạo output directory\n", "output_path = \"/content/drive/MyDrive/TTS/MyOutput\"\n", "os.makedirs(output_path, exist_ok=True)\n", "print(f\"📁 Output directory: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "config"}, "source": ["## ⚙️ 3. Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config_code"}, "outputs": [], "source": ["import os\n", "import re\n", "from trainer import Trainer, TrainerArgs\n", "from TTS.tts.configs.shared_configs import BaseDatasetConfig\n", "from TTS.tts.configs.vits_config import VitsConfig\n", "from TTS.tts.datasets import load_tts_samples\n", "from TTS.tts.models.vits import Vits, VitsArgs, VitsAudioConfig\n", "from TTS.tts.utils.text.tokenizer import TTSTokenizer\n", "from TTS.utils.audio import AudioProcessor\n", "\n", "# Text cleaner cho ti<PERSON><PERSON>\n", "def vietnamese_cleaners(text):\n", "    \"\"\"\n", "    Cleaner t<PERSON>i ưu cho tiếng Việt - giữ lại dấu câu cảm xúc\n", "    \"\"\"\n", "    text = text.lower()\n", "    \n", "    # <PERSON><PERSON><PERSON> lại ký tự tiếng <PERSON>, s<PERSON>, và dấu câu quan trọng\n", "    text = re.sub(r'[^a-zA-Zàáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ0-9\\s,.!?\\'\\\":-]', '', text)\n", "    \n", "    # <PERSON><PERSON><PERSON> hóa khoảng trắng và dấu câu\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    text = re.sub(r'([,.!?])\\s*', r'\\1 ', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "# <PERSON><PERSON><PERSON> k<PERSON>\n", "from TTS.tts.utils.text import cleaners\n", "cleaners.vietnamese_cleaners = vietnamese_cleaners\n", "\n", "print(\"✅ Vietnamese text cleaner registered!\")\n", "\n", "# Test cleaner\n", "test_text = \"Xin chào! Bạn có khỏe không? Tôi rất vui được gặp bạn.\"\n", "cleaned = vietnamese_cleaners(test_text)\n", "print(f\"\\n🧪 Cleaner test:\")\n", "print(f\"  Input:  {test_text}\")\n", "print(f\"  Output: {cleaned}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_config"}, "outputs": [], "source": ["# Cấu hình dataset\n", "dataset_config = BaseDatasetConfig(\n", "    formatter=\"ljspeech\",\n", "    meta_file_train=\"metadata.txt\",\n", "    path=\"/content/drive/MyDrive/TTS/MyTTSDataset\",\n", "    language=\"vi\"\n", ")\n", "\n", "# <PERSON><PERSON><PERSON> hình audio\n", "audio_config = VitsAudioConfig(\n", "    sample_rate=22050,\n", "    win_length=1024,\n", "    hop_length=256,\n", "    num_mels=80,\n", "    mel_fmin=0,\n", "    mel_fmax=None,\n", ")\n", "\n", "# <PERSON><PERSON><PERSON> hình VITS\n", "vits_args = VitsArgs(\n", "    use_speaker_embedding=False,\n", "    use_sdp=True,\n", "    noise_scale=0.667,\n", "    noise_scale_dp=1.0,\n", "    length_scale=1.0,\n", ")\n", "\n", "# <PERSON><PERSON><PERSON> hình ch<PERSON>h\n", "config = VitsConfig(\n", "    model_args=vits_args,\n", "    audio=audio_config,\n", "    run_name=\"vits_vietnamese_colab\",\n", "    \n", "    # Batch size tối <PERSON>u cho <PERSON>\n", "    batch_size=8,  # <PERSON><PERSON><PERSON><PERSON> để tr<PERSON>h <PERSON>\n", "    eval_batch_size=4,\n", "    batch_group_size=5,\n", "    \n", "    # Workers\n", "    num_loader_workers=2,\n", "    num_eval_loader_workers=2,\n", "    \n", "    # Training\n", "    run_eval=True,\n", "    test_delay_epochs=10,\n", "    epochs=1000,\n", "    \n", "    # Text processing\n", "    text_cleaner=\"vietnamese_cleaners\",\n", "    use_phonemes=False,\n", "    phoneme_language=\"vi\",\n", "    phoneme_cache_path=os.path.join(output_path, \"phoneme_cache\"),\n", "    compute_input_seq_cache=True,\n", "    \n", "    # Logging\n", "    print_step=25,\n", "    print_eval=True,\n", "    save_step=500,\n", "    save_checkpoints=True,\n", "    \n", "    # Optimization\n", "    mixed_precision=True,\n", "    grad_clip=[1000, 1000],\n", "    \n", "    # Paths\n", "    output_path=output_path,\n", "    datasets=[dataset_config],\n", "    \n", "    # Test sentences\n", "    test_sentences=[\n", "        \"<PERSON><PERSON> chào, tôi là trợ lý ảo tiếng Việt!\",\n", "        \"Hôm nay trời đẹp quá, bạn có muốn đi dạo không?\",\n", "        \"Cảm ơn bạn rất nhiều vì đã giúp đỡ tôi.\",\n", "        \"<PERSON><PERSON><PERSON> bạn một ngày tốt lành và hạnh phúc!\",\n", "        \"T<PERSON><PERSON> yêu V<PERSON> Nam và tiếng Việt của chúng ta.\"\n", "    ],\n", "    \n", "    # Character set\n", "    characters={\n", "        \"characters\": \"aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz0123456789\",\n", "        \"punctuations\": \",.!?'\\\":-\",\n", "        \"pad\": \"<PAD>\",\n", "        \"eos\": \"<EOS>\",\n", "        \"bos\": \"<BOS>\",\n", "        \"blank\": \"<BLNK>\",\n", "    }\n", ")\n", "\n", "print(\"✅ Configuration created!\")\n", "print(f\"📦 Batch size: {config.batch_size}\")\n", "print(f\"🔄 Epochs: {config.epochs}\")\n", "print(f\"🎵 Sample rate: {config.audio.sample_rate}Hz\")\n", "print(f\"💾 Mixed precision: {config.mixed_precision}\")"]}, {"cell_type": "markdown", "metadata": {"id": "training"}, "source": ["## 🚀 4. Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "init_components"}, "outputs": [], "source": ["# Khởi tạo components\n", "print(\"🔧 Initializing components...\")\n", "\n", "ap = AudioProcessor.init_from_config(config)\n", "tokenizer, config = TTSTokenizer.init_from_config(config)\n", "\n", "print(\"✅ Audio processor and tokenizer initialized!\")\n", "\n", "# Load dataset\n", "print(\"📊 Loading dataset...\")\n", "train_samples, eval_samples = load_tts_samples(\n", "    dataset_config,\n", "    eval_split=True,\n", "    eval_split_max_size=config.eval_split_max_size,\n", "    eval_split_size=config.eval_split_size,\n", ")\n", "\n", "print(f\"✅ Dataset loaded:\")\n", "print(f\"  📈 Training samples: {len(train_samples)}\")\n", "print(f\"  📊 Evaluation samples: {len(eval_samples)}\")\n", "\n", "# <PERSON><PERSON><PERSON> tra một vài samples\n", "print(\"\\n🔍 Sample data check:\")\n", "for i, sample in enumerate(train_samples[:3]):\n", "    print(f\"  {i+1}. {sample['text'][:50]}...\")\n", "    print(f\"     Audio: {sample['audio_file']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "init_model"}, "outputs": [], "source": ["# Khởi tạo model\n", "print(\"🤖 Initializing VITS model...\")\n", "model = Vits(config, ap, tokenizer)\n", "\n", "# Đếm parameters\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "print(f\"✅ Model initialized!\")\n", "print(f\"  📊 Total parameters: {total_params:,}\")\n", "print(f\"  🎯 Trainable parameters: {trainable_params:,}\")\n", "print(f\"  💾 Model size: ~{total_params * 4 / 1e6:.1f} MB\")\n", "\n", "# Setup trainer\n", "trainer_args = TrainerArgs(\n", "    restore_path=None,\n", "    skip_train_epoch=False,\n", "    start_with_eval=False,\n", "    grad_accum_steps=1,\n", ")\n", "\n", "print(\"\\n🎯 Training will start with:\")\n", "print(f\"  📁 Output: {output_path}\")\n", "print(f\"  🌍 Language: Vietnamese\")\n", "print(f\"  🔧 Model: VITS\")\n", "print(f\"  📝 Text cleaner: vietnamese_cleaners\")\n", "print(f\"  🎵 Sample rate: {config.audio.sample_rate}Hz\")\n", "print(f\"  📦 Batch size: {config.batch_size}\")\n", "print(f\"  🔄 Epochs: {config.epochs}\")\n", "print(f\"  💾 Mixed precision: {config.mixed_precision}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "start_training"}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> đầu training\n", "print(\"🚀 Starting training...\")\n", "print(\"=\" * 50)\n", "\n", "trainer = Trainer(\n", "    trainer_args,\n", "    config,\n", "    output_path,\n", "    model=model,\n", "    train_samples=train_samples,\n", "    eval_samples=eval_samples\n", ")\n", "\n", "# Chạy training\n", "trainer.fit()"]}, {"cell_type": "markdown", "metadata": {"id": "monitoring"}, "source": ["## 📊 5. Monitoring & Results\n", "\n", "Trong quá trình training, bạn có thể:\n", "\n", "1. **<PERSON> logs**: Xem loss và metrics trong output\n", "2. **<PERSON><PERSON><PERSON> tra checkpoints**: Trong `/content/drive/MyDrive/TTS/MyOutput`\n", "3. **<PERSON>he audio samples**: Model sẽ tạo test samples đ<PERSON><PERSON> kỳ\n", "4. **TensorBoard**: <PERSON><PERSON><PERSON> có thể, sử dụng TensorBoard để visualize\n", "\n", "### 🎯 Các chỉ số quan trọng:\n", "- **Generator Loss**: <PERSON><PERSON><PERSON>\n", "- **Discriminator Loss**: <PERSON><PERSON><PERSON> <PERSON><PERSON>\n", "- **Mel Loss**: <PERSON><PERSON><PERSON>\n", "- **Duration Loss**: <PERSON><PERSON><PERSON>\n", "\n", "### 💡 Tips:\n", "- Training c<PERSON> thể mất 6-12 gi<PERSON> trê<PERSON>\n", "- <PERSON><PERSON><PERSON> tra audio samples sau ~100 epochs\n", "- Model tốt thường cần 500-1000 epochs\n", "- Backup checkpoints thư<PERSON><PERSON>n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}