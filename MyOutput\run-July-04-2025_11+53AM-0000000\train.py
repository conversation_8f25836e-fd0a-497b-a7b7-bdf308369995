# code for train.py to be created in TTS Folder

import os
from trainer import Trainer, TrainerArgs
from TTS.config.shared_configs import BaseAudioConfig
from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.tacotron2_config import Tacotron2Config
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.tacotron2 import Tacotron2
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor

# A folder is specified to contain the necessary logs related the training 
# process to resume later. It will be created automatically. No manual 
# intervention needed

output_path = '/content/drive/MyDrive/TTS/MyOutput' 

# specify the path that contains the training data

file_path = '/content/drive/MyDrive/TTS/MyTTSDataset'

# init the dataset and audio configs

dataset_config = BaseDatasetConfig(
    formatter="ljspeech", meta_file_train="metadata.txt", path=file_path, language="vi-vn")


audio_config = BaseAudioConfig(
    sample_rate=22050,
    do_trim_silence=True,
    trim_db=60.0,
    signal_norm=False,
    mel_fmin=0.0,
    mel_fmax=8000,
    spec_gain=1.0,
    log_func="np.log",
    ref_level_db=20,
    preemphasis=0.0,
)

# This is the config that is saved for the future use
# change the hyper parameters like batch size, epochs and other factors here
# A few parameters have "##" beside them which you might need to change 
# suited according to the the dataset you are using and the hardware 
# configurations of your machine. Some discussions on it is done in 'Tips 
# and Tricks' section in the last


config = Tacotron2Config(  
    audio=audio_config,
    batch_size=64,      ##
    eval_batch_size=16,
    num_loader_workers=2, ##
    num_eval_loader_workers=2, ##
    run_eval=True,
    test_delay_epochs=-1,
    ga_alpha=0.0,
    decoder_loss_alpha=0.25,
    postnet_loss_alpha=0.25,
    postnet_diff_spec_alpha=0,
    decoder_diff_spec_alpha=0,
    decoder_ssim_alpha=0,
    postnet_ssim_alpha=0,
    r=2,
    attention_type="dynamic_convolution",
    double_decoder_consistency=False,
    epochs=1000,        ## 
    text_cleaner="basic_cleaners",
    use_phonemes=False,
    phoneme_language="vi-vn",
    phoneme_cache_path=os.path.join(output_path, "phoneme_cache"),
    print_step=25,
    print_eval=True,
    mixed_precision=False,
    output_path=output_path,
    datasets=[dataset_config],
    save_checkpoints=True,
    save_step = 100,
)

# INITIALIZE THE AUDIO PROCESSOR
# Audio processor is used for feature extraction and audio I/O.
# It mainly serves to the dataloader and the training loggers.

ap = AudioProcessor.init_from_config(config)

# INITIALIZE THE TOKENIZER
# Tokenizer is used to convert text to sequences of token IDs.

tokenizer, config = TTSTokenizer.init_from_config(config)

# LOAD DATA SAMPLES
train_samples, eval_samples = load_tts_samples(
    dataset_config,
    eval_split=True,
    eval_split_max_size=config.eval_split_max_size,
    eval_split_size=config.eval_split_size,
)

# INITIALIZE THE MODEL
# Models take a config object and a speaker manager as input
# Config defines the details of the model like the number of layers, 
# the size of the embedding, etc.

model = Tacotron2(config, ap, tokenizer)

# INITIALIZE THE TRAINER
# Trainer provides a generic API to train all the TTS models
# with all its perks like mixed-precision training,
# distributed training, etc.

trainer = Trainer(
    TrainerArgs(), config, output_path, model=model, train_samples=train_samples, eval_samples=eval_samples
)
trainer.fit()