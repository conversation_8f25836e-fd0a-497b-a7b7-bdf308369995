import os
import re
from trainer import Trainer, TrainerArgs
from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.vits import Vits, VitsArgs, VitsAudioConfig
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor

# Hàm cleaner tùy chỉnh cho tiếng Việt - giữ lại dấu câu để bảo toàn cảm xúc
def vietnamese_cleaners(text):
    """
    Cleaner cho tiếng <PERSON>, giữ lại dấu câu quan trọng để bảo toàn cảm xúc
    """
    # Chuyển về chữ thường nhưng giữ nguyên dấu câu
    text = text.lower()

    # Chỉ loại bỏ các ký tự không mong muốn, giữ lại dấu câu cảm xúc
    # Giữ lại: chữ cái tiế<PERSON>, s<PERSON>, k<PERSON><PERSON><PERSON> tr<PERSON>, và các dấu câu quan trọng
    text = re.sub(r'[^a-zA-Zàáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ0-9\s,.!?\'\":-]', '', text)

    # Chuẩn hóa khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()

    # Chuẩn hóa dấu câu - thêm khoảng trắng sau dấu câu nếu cần
    text = re.sub(r'([,.!?])\s*', r'\1 ', text)
    text = re.sub(r'\s+', ' ', text).strip()

    return text

# Cấu hình đường dẫn cho Colab
output_path = '/content/drive/MyDrive/TTS/MyOutput'
file_path = '/content/drive/MyDrive/TTS/MyTTSDataset'

# Cấu hình dataset - sửa formatter từ "ljsotch" thành "ljspeech"
dataset_config = BaseDatasetConfig(
    formatter="ljspeech",
    meta_file_train="metadata.txt",
    path=file_path,
    language="vi"  # Đổi từ "vi-vn" thành "vi" để tương thích tốt hơn
)

# Cấu hình audio tối ưu cho VITS và tiếng Việt
audio_config = VitsAudioConfig(
    sample_rate=22050,  # Tần số mẫu chuẩn cho TTS
    win_length=1024,    # Độ dài cửa sổ
    hop_length=256,     # Bước nhảy
    num_mels=80,        # Số mel bands
    mel_fmin=0,         # Tần số mel tối thiểu
    mel_fmax=None,      # Tần số mel tối đa (None = sample_rate/2)
)

# Cấu hình VITS args
vits_args = VitsArgs(
    use_speaker_embedding=False,  # Single speaker dataset
    use_sdp=True,                 # Stochastic Duration Predictor
    noise_scale=0.667,            # Noise scale for inference
    noise_scale_dp=1.0,           # Noise scale for duration predictor
    length_scale=1.0,             # Length scale for inference
)

# Cấu hình VITS - tối ưu cho Colab và tiếng Việt
config = VitsConfig(
    model_args=vits_args,
    audio=audio_config,
    run_name="vits_vietnamese",

    # Batch size tối ưu cho Colab (GPU T4/V100)
    batch_size=16,               # Giảm để tránh OOM trên Colab
    eval_batch_size=8,
    batch_group_size=5,          # Nhóm batch theo độ dài

    # Workers - giảm cho Colab
    num_loader_workers=2,
    num_eval_loader_workers=2,

    # Training settings
    run_eval=True,
    test_delay_epochs=10,        # Bắt đầu eval sau 10 epochs
    epochs=1000,                 # Số epochs training

    # Text processing - quan trọng cho tiếng Việt
    text_cleaner="vietnamese_cleaners",
    use_phonemes=False,          # Không dùng phonemes cho tiếng Việt
    phoneme_language="vi",
    phoneme_cache_path=os.path.join(output_path, "phoneme_cache"),
    compute_input_seq_cache=True,

    # Logging và monitoring
    print_step=25,
    print_eval=True,
    save_step=1000,
    save_checkpoints=True,

    # Optimization
    mixed_precision=True,        # Quan trọng cho Colab
    grad_clip=[1000, 1000],      # Gradient clipping

    # Paths
    output_path=output_path,
    datasets=[dataset_config],

    # Test sentences cho tiếng Việt
    test_sentences=[
        "Xin chào, tôi là trợ lý ảo tiếng Việt.",
        "Hôm nay trời đẹp quá!",
        "Bạn có khỏe không?",
        "Cảm ơn bạn rất nhiều.",
        "Chúc bạn một ngày tốt lành!"
    ],

    # Character set cho tiếng Việt - bao gồm dấu câu cảm xúc
    characters={
        "characters": "aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz0123456789",
        "punctuations": ",.!?'\":-",  # Mở rộng dấu câu để giữ cảm xúc
        "pad": "<PAD>",
        "eos": "<EOS>",
        "bos": "<BOS>",
        "blank": "<BLNK>",
    }
)

# Khởi tạo AudioProcessor
ap = AudioProcessor.init_from_config(config)

# Đăng ký custom cleaner
from TTS.tts.utils.text import cleaners
cleaners.vietnamese_cleaners = vietnamese_cleaners

# Khởi tạo tokenizer
tokenizer, config = TTSTokenizer.init_from_config(config)

# Load dataset samples
train_samples, eval_samples = load_tts_samples(
    dataset_config,
    eval_split=True,
    eval_split_max_size=config.eval_split_max_size,
    eval_split_size=config.eval_split_size,
)

print(f"📊 Dataset loaded: {len(train_samples)} training samples, {len(eval_samples)} eval samples")

# Kiểm tra ký tự không hợp lệ trong dataset
def check_invalid_chars(text):
    valid_chars = set("aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz0123456789,.!?'\":-<> ")
    return [c for c in text if c not in valid_chars]

print("🔍 Checking for invalid characters in dataset...")
invalid_count = 0
for i, sample in enumerate(train_samples[:10]):  # Chỉ kiểm tra 10 samples đầu
    invalid = check_invalid_chars(sample['text'])
    if invalid:
        print(f"⚠️  Invalid chars in sample {i}: '{sample['text']}' -> {set(invalid)}")
        invalid_count += 1

if invalid_count == 0:
    print("✅ No invalid characters found in sample check!")
else:
    print(f"⚠️  Found invalid characters in {invalid_count} samples")

# Khởi tạo VITS model
print("🚀 Initializing VITS model...")
model = Vits(config, ap, tokenizer)

# Cấu hình trainer
trainer_args = TrainerArgs(
    restore_path=None,  # Set path nếu muốn resume training
    skip_train_epoch=False,
    start_with_eval=False,
    grad_accum_steps=1,
)

print("🎯 Starting training...")
print(f"📁 Output path: {output_path}")
print(f"🔧 Model: VITS")
print(f"🌍 Language: Vietnamese")
print(f"📝 Text cleaner: vietnamese_cleaners")
print(f"🎵 Sample rate: {config.audio.sample_rate}Hz")
print(f"📦 Batch size: {config.batch_size}")
print(f"🔄 Epochs: {config.epochs}")

# Khởi tạo và chạy trainer
trainer = Trainer(
    trainer_args,
    config,
    output_path,
    model=model,
    train_samples=train_samples,
    eval_samples=eval_samples
)

# Bắt đầu training
trainer.fit()