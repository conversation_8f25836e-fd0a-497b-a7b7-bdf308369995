import os
import re
from trainer import Trainer, TrainerArgs
from TTS.config.shared_configs import BaseAudioConfig
from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.tacotron2_config import Tacotron2Config
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.tacotron2 import Tacotron2
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor

# Hàm cleaner tùy chỉnh cho tiếng Việt
def vietnamese_cleaners(text):
    text = text.lower()
    text = re.sub(r'[^\w\sàáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ,\'!?"\.]', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text

output_path = '/content/drive/MyDrive/TTS/MyOutput'
file_path = '/content/drive/MyDrive/TTS/MyTTSDataset'

dataset_config = BaseDatasetConfig(
    formatter="ljsotch", meta_file_train="metadata.txt", path=file_path, language="vi-vn")

audio_config = BaseAudioConfig(
    sample_rate=22050,
    do_trim_silence=True,
    trim_db=60.0,
    signal_norm=False,
    mel_fmin=0.0,
    mel_fmax=8000,
    spec_gain=1.0,
    log_func="np.log",
    ref_level_db=20,
    preemphasis=0.0,
)

config = Tacotron2Config(
    audio=audio_config,
    batch_size=32,  # Giảm để tránh lỗi bộ nhớ
    eval_batch_size=16,
    num_loader_workers=2,
    num_eval_loader_workers=2,
    run_eval=True,
    test_delay_epochs=-1,
    ga_alpha=0.0,
    decoder_loss_alpha=0.25,
    postnet_loss_alpha=0.25,
    postnet_diff_spec_alpha=0,
    decoder_diff_spec_alpha=0,
    decoder_ssim_alpha=0,
    postnet_ssim_alpha=0,
    r=2,
    attention_type="dynamic_convolution",
    double_decoder_consistency=False,
    epochs=500,  # Giảm để thử nghiệm
    text_cleaner="vietnamese_cleaners",
    use_phonemes=False,
    phoneme_language="vi-vn",
    phoneme_cache_path=os.path.join(output_path, "phoneme_cache"),
    print_step=25,
    print_eval=True,
    mixed_precision=True,  # Bật để tối ưu
    output_path=output_path,
    datasets=[dataset_config],
    save_checkpoints=True,
    save_step=100,
    characters={
        "characters": "aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz",
        "punctuations": ",.!?'\"",
        "pad": "_",
        "eos": "~",
        "bos": "^"
    }
)

ap = AudioProcessor.init_from_config(config)

from TTS.tts.utils.text import cleaners
cleaners.vietnamese_cleaners = vietnamese_cleaners

tokenizer, config = TTSTokenizer.init_from_config(config)

train_samples, eval_samples = load_tts_samples(
    dataset_config,
    eval_split=True,
    eval_split_max_size=config.eval_split_max_size,
    eval_split_size=config.eval_split_size,
)

# Kiểm tra ký tự không hợp lệ
def check_invalid_chars(text):
    valid_chars = set("aáàảãạăắằẳẵặâấầẩẫậbcdđeéèẻẽẹêếềểễệfghiíìỉĩịjklmnoóòỏõọôốồổỗộơớờởỡợpqrstuúùủũụưứừửữựvwxyýỳỷỹỵz,.!?'\"")
    return [c for c in text if c not in valid_chars]

for sample in train_samples:
    invalid = check_invalid_chars(sample['text'])
    if invalid:
        print(f"Invalid chars in {sample['text']}: {invalid}")

model = Tacotron2(config, ap, tokenizer)

trainer = Trainer(
    TrainerArgs(), config, output_path, model=model, train_samples=train_samples, eval_samples=eval_samples
)
trainer.fit()