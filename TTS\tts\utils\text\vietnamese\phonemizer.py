from typing import Dict, List
import re
from TTS.tts.utils.text.phonemizers.base import BasePhonemizer

# Define Vietnamese punctuations
_DEF_VI_PUNCS = "!¡?¿.。,،:;।॥-—…«»""''()[]{}@#†‡*&*•⁂❧☙♪♫♬♭♮♯"

# Vietnamese phoneme mappings
# Initial consonants
_INITIALS = {
    'b': 'b', 'c': 'k', 'd': 'z', 'đ': 'd', 'g': 'ɣ', 'gh': 'ɣ', 'gi': 'z', 'h': 'h',
    'k': 'k', 'kh': 'x', 'l': 'l', 'm': 'm', 'n': 'n', 'ng': 'ŋ', 'ngh': 'ŋ', 'nh': 'ɲ',
    'p': 'p', 'ph': 'f', 'q': 'k', 'qu': 'kw', 'r': 'z', 's': 's', 't': 't', 'th': 'tʰ',
    'tr': 'ʈ', 'v': 'v', 'x': 's', 'ch': 'c'
}

# Vowels (nucleus)
_VOWELS = {
    'a': 'a', 'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
    'ă': 'ă', 'ằ': 'ă', 'ắ': 'ă', 'ẳ': 'ă', 'ẵ': 'ă', 'ặ': 'ă',
    'â': 'ə', 'ầ': 'ə', 'ấ': 'ə', 'ẩ': 'ə', 'ẫ': 'ə', 'ậ': 'ə',
    'e': 'ɛ', 'è': 'ɛ', 'é': 'ɛ', 'ẻ': 'ɛ', 'ẽ': 'ɛ', 'ẹ': 'ɛ',
    'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
    'i': 'i', 'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
    'o': 'ɔ', 'ò': 'ɔ', 'ó': 'ɔ', 'ỏ': 'ɔ', 'õ': 'ɔ', 'ọ': 'ɔ',
    'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
    'ơ': 'əː', 'ờ': 'əː', 'ớ': 'əː', 'ở': 'əː', 'ỡ': 'əː', 'ợ': 'əː',
    'u': 'u', 'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
    'ư': 'ɨ', 'ừ': 'ɨ', 'ứ': 'ɨ', 'ử': 'ɨ', 'ữ': 'ɨ', 'ự': 'ɨ',
    'y': 'i', 'ỳ': 'i', 'ý': 'i', 'ỷ': 'i', 'ỹ': 'i', 'ỵ': 'i'
}

# Final consonants (coda)
_FINALS = {
    'c': 'k', 'm': 'm', 'n': 'n', 'ng': 'ŋ', 'nh': 'ɲ', 'p': 'p', 't': 't', 'ch': 'k'
}

# Tones
_TONES = {
    'à': '2', 'á': '1', 'ả': '3', 'ã': '4', 'ạ': '5',
    'ằ': '2', 'ắ': '1', 'ẳ': '3', 'ẵ': '4', 'ặ': '5',
    'ầ': '2', 'ấ': '1', 'ẩ': '3', 'ẫ': '4', 'ậ': '5',
    'è': '2', 'é': '1', 'ẻ': '3', 'ẽ': '4', 'ẹ': '5',
    'ề': '2', 'ế': '1', 'ể': '3', 'ễ': '4', 'ệ': '5',
    'ì': '2', 'í': '1', 'ỉ': '3', 'ĩ': '4', 'ị': '5',
    'ò': '2', 'ó': '1', 'ỏ': '3', 'õ': '4', 'ọ': '5',
    'ồ': '2', 'ố': '1', 'ổ': '3', 'ỗ': '4', 'ộ': '5',
    'ờ': '2', 'ớ': '1', 'ở': '3', 'ỡ': '4', 'ợ': '5',
    'ù': '2', 'ú': '1', 'ủ': '3', 'ũ': '4', 'ụ': '5',
    'ừ': '2', 'ứ': '1', 'ử': '3', 'ữ': '4', 'ự': '5',
    'ỳ': '2', 'ý': '1', 'ỷ': '3', 'ỹ': '4', 'ỵ': '5'
}

# Vowel combinations
_VOWEL_COMBINATIONS = {
    'ai': 'aj', 'ay': 'ăj', 'ao': 'aw', 'au': 'ăw', 'eo': 'ew', 'ia': 'iə', 'ie': 'iə',
    'iu': 'iw', 'oa': 'wa', 'oe': 'wɛ', 'oi': 'ɔj', 'oo': 'ɔ', 'ua': 'uə', 'ue': 'uɛ',
    'ui': 'uj', 'uo': 'uə', 'uu': 'u', 'uy': 'uj', 'ưa': 'ɨə', 'ưi': 'ɨj', 'ươ': 'ɨə',
    'ưu': 'ɨw', 'ye': 'iɛ', 'yê': 'iɛ', 'uô': 'uə', 'uơ': 'uə', 'ươ': 'ɨə', 'uyê': 'wiə',
    'oa': 'wa', 'oă': 'wă', 'oe': 'wɛ', 'uê': 'we', 'uy': 'wi'
}

def normalize_vietnamese_text(text: str) -> str:
    """Normalize Vietnamese text by removing extra spaces and lowercasing."""
    text = re.sub(r'\s+', ' ', text).strip()
    return text.lower()

def extract_syllable_components(syllable: str) -> tuple:
    """Extract initial, nucleus, coda and tone from a Vietnamese syllable."""
    syllable = syllable.lower()
    
    # Default values
    initial = ''
    nucleus = syllable
    coda = ''
    tone = '0'  # Neutral tone
    
    # Extract tone first
    for char in syllable:
        if char in _TONES:
            tone = _TONES[char]
            break
    
    # Extract initial consonant
    for init in sorted(_INITIALS.keys(), key=len, reverse=True):
        if syllable.startswith(init):
            initial = _INITIALS[init]
            nucleus = syllable[len(init):]
            break
    
    # Extract final consonant
    for final in sorted(_FINALS.keys(), key=len, reverse=True):
        if nucleus.endswith(final):
            coda = _FINALS[final]
            nucleus = nucleus[:-len(final)]
            break
    
    # Process nucleus (vowels)
    processed_nucleus = ''
    # Check for vowel combinations first
    found_combo = False
    for combo, phoneme in sorted(_VOWEL_COMBINATIONS.items(), key=lambda x: len(x[0]), reverse=True):
        if combo in nucleus:
            processed_nucleus = phoneme
            found_combo = True
            break
    
    # If no combination found, process individual vowels
    if not found_combo:
        for char in nucleus:
            if char in _VOWELS:
                processed_nucleus += _VOWELS[char]
            else:
                processed_nucleus += char
    
    return initial, processed_nucleus, coda, tone

def vietnamese_text_to_phonemes(text: str) -> str:
    """Convert Vietnamese text to phonemes."""
    text = normalize_vietnamese_text(text)
    words = text.split()
    phonemes = []
    
    for word in words:
        # Check if it's a punctuation
        if all(char in _DEF_VI_PUNCS for char in word):
            phonemes.append(word)
            continue
        
        # Split into syllables (Vietnamese words can have multiple syllables)
        syllables = re.findall(r'[^\s]+', word)
        word_phonemes = []
        
        for syllable in syllables:
            initial, nucleus, coda, tone = extract_syllable_components(syllable)
            
            # Combine components into phoneme representation
            syllable_phoneme = ''
            if initial:
                syllable_phoneme += initial + ' '
            syllable_phoneme += nucleus
            if coda:
                syllable_phoneme += ' ' + coda
            
            # Add tone marker
            if tone != '0':
                syllable_phoneme += tone
                
            word_phonemes.append(syllable_phoneme)
        
        phonemes.append(' '.join(word_phonemes))
    
    return ' '.join(phonemes)

class VI_Phonemizer(BasePhonemizer):
    """🐸TTS Vietnamese phonemizer.

    Args:
        punctuations (str):
            Set of characters to be treated as punctuation. Defaults to `_DEF_VI_PUNCS`.

        keep_puncs (bool):
            If True, keep the punctuations after phonemization. Defaults to False.
    """

    language = "vi"

    def __init__(self, punctuations=_DEF_VI_PUNCS, keep_puncs=False, **kwargs):
        super().__init__(self.language, punctuations=punctuations, keep_puncs=keep_puncs)

    @staticmethod
    def name():
        return "vi_phonemizer"

    def _phonemize(self, text: str, separator: str = " ") -> str:
        ph = vietnamese_text_to_phonemes(text)
        if separator is not None and separator != " ":
            ph = ph.replace(" ", separator)
        return ph

    def phonemize(self, text: str, separator: str = " ", language=None) -> str:
        return self._phonemize(text, separator)

    @staticmethod
    def supported_languages() -> Dict:
        return {"vi": "Vietnamese"}

    def version(self) -> str:
        return "0.1.0"

    def phonemize_list(self, text_list: List[str], separator: str = " ") -> List[str]:
        return [self.phonemize(text, separator=separator) for text in text_list]